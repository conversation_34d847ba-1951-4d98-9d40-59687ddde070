/* ===== JOBS PAGE SPECIFIC STYLES ===== */

/* Main Content Fix */
.main {
  margin-top: 4rem; /* Account for fixed header height */
}

/* Jobs Hero Section */
.jobs-hero {
  padding: 6rem 0 4rem;
  background: var(--gradient-hero);
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.jobs-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  animation: float 20s ease-in-out infinite;
}

.jobs-hero__content {
  position: relative;
  z-index: 2;
  max-width: 900px;
  margin: 0 auto;
}

.jobs-hero__title {
  font-size: var(--h1-font-size);
  font-weight: var(--font-extra-bold);
  margin-bottom: var(--mb-1);
  line-height: 1.1;
}

.jobs-hero__subtitle {
  font-size: 1.1rem;
  margin-bottom: var(--mb-3);
  opacity: 0.9;
  line-height: 1.6;
}

/* Quick Search */
.quick-search {
  margin-bottom: var(--mb-3);
}

.search-form {
  display: flex;
  gap: var(--mb-1);
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius-lg);
  padding: 1rem;
}

.search-input-group,
.location-input-group {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--border-radius);
  padding: 0 1rem;
}

.search-input-group i,
.location-input-group i {
  color: var(--text-color-light);
  margin-right: 0.5rem;
}

.quick-search-input,
.quick-location-select {
  flex: 1;
  border: none;
  background: transparent;
  padding: 1rem 0;
  font-size: var(--normal-font-size);
  color: var(--text-color);
  outline: none;
}

.quick-location-select {
  cursor: pointer;
}

.btn-search {
  padding: 1rem 2rem;
  white-space: nowrap;
}

/* Job Stats */
.job-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--mb-2);
  max-width: 600px;
  margin: 0 auto;
}

.stat-item {
  text-align: center;
}

.stat-item h3 {
  font-size: 2rem;
  font-weight: var(--font-extra-bold);
  margin-bottom: 0.5rem;
  color: var(--secondary-color);
}

.stat-item p {
  font-size: var(--small-font-size);
  opacity: 0.9;
}

/* Job Filters */
.job-filters {
  background-color: var(--body-color);
  border-bottom: 1px solid var(--border-color);
}

.job-filters-panel {
  background: var(--container-color);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  margin-bottom: var(--mb-2);
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.job-filters-panel.active {
  opacity: 1;
  max-height: 600px;
}

/* Job Listings */
.job-listings {
  padding-top: 2rem;
}

.listings-stats {
  margin-bottom: var(--mb-2);
  text-align: center;
}

.jobs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--mb-2);
  margin-bottom: var(--mb-3);
}

.job-card {
  background: var(--card-color);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  border: 1px solid var(--border-color);
  position: relative;
  cursor: pointer;
}

.job-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.job-card.featured::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-secondary);
}

.job-card.urgent::before {
  background: var(--error-color);
}

.job-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--mb-1);
}

.job-company {
  display: flex;
  align-items: center;
  gap: var(--mb-0-75);
}

.company-logo {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius);
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  font-weight: var(--font-bold);
}

.company-info h4 {
  font-size: var(--normal-font-size);
  font-weight: var(--font-semi-bold);
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.company-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--secondary-color);
  font-size: var(--small-font-size);
}

.job-badges {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  align-items: flex-end;
}

.job-badge {
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius);
  font-size: var(--smaller-font-size);
  font-weight: var(--font-medium);
}

.job-badge.featured {
  background: var(--secondary-color);
  color: white;
}

.job-badge.urgent {
  background: var(--error-color);
  color: white;
}

.job-badge.remote {
  background: var(--accent-color);
  color: white;
}

.job-title {
  font-size: var(--h3-font-size);
  color: var(--text-color);
  margin-bottom: var(--mb-0-5);
  line-height: 1.3;
}

.job-description {
  color: var(--text-color-light);
  font-size: var(--small-font-size);
  margin-bottom: var(--mb-1);
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.job-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--mb-0-5);
  margin-bottom: var(--mb-1);
}

.job-detail {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--small-font-size);
  color: var(--text-color-light);
}

.job-detail i {
  color: var(--primary-color);
  width: 16px;
  flex-shrink: 0;
}

.job-detail.salary {
  color: var(--success-color);
  font-weight: var(--font-medium);
}

.job-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--mb-1);
  padding-top: var(--mb-1);
  border-top: 1px solid var(--border-color);
}

.job-posted {
  font-size: var(--smaller-font-size);
  color: var(--text-color-lighter);
}

.job-actions {
  display: flex;
  gap: var(--mb-0-5);
}

.btn-job {
  padding: 0.5rem 1rem;
  font-size: var(--small-font-size);
}

.btn-job-small {
  padding: 0.5rem 0.75rem;
  font-size: var(--smaller-font-size);
}

/* Partner Information Section */
.partner-info {
  background: var(--container-color);
}

.partner-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--mb-3);
  align-items: start;
}

.partner-text .section__title {
  text-align: left;
  margin-bottom: var(--mb-1);
}

.partner-text p {
  color: var(--text-color-light);
  margin-bottom: var(--mb-2);
  line-height: 1.6;
}

.partner-benefits {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--mb-1);
  margin-bottom: var(--mb-2);
}

.benefit {
  display: flex;
  align-items: center;
  gap: var(--mb-0-5);
  padding: 0.75rem;
  background: var(--body-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.benefit:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.benefit i {
  font-size: 1.25rem;
  color: var(--primary-color);
  flex-shrink: 0;
}

.benefit:hover i {
  color: white;
}

.benefit span {
  font-weight: var(--font-medium);
  font-size: var(--small-font-size);
}

.partner-actions {
  display: flex;
  gap: var(--mb-1);
  flex-wrap: wrap;
}

/* Partner Plans */
.partner-plans h3 {
  text-align: center;
  margin-bottom: var(--mb-2);
  color: var(--text-color);
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--mb-1-5);
}

.plan-card {
  background: var(--card-color);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  text-align: center;
  border: 1px solid var(--border-color);
  transition: var(--transition);
  position: relative;
}

.plan-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.plan-card.featured {
  border-color: var(--primary-color);
  transform: scale(1.05);
}

.plan-card.featured::before {
  content: 'Popular';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 1rem;
  border-radius: var(--border-radius);
  font-size: var(--smaller-font-size);
  font-weight: var(--font-bold);
}

.plan-card h4 {
  font-size: var(--h3-font-size);
  color: var(--text-color);
  margin-bottom: var(--mb-1);
}

.plan-price {
  font-size: 1.5rem;
  font-weight: var(--font-bold);
  color: var(--primary-color);
  margin-bottom: var(--mb-1);
}

.plan-price span {
  font-size: var(--small-font-size);
  color: var(--text-color-light);
  font-weight: var(--font-regular);
}

.plan-features {
  list-style: none;
  padding: 0;
}

.plan-features li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0;
  font-size: var(--small-font-size);
  color: var(--text-color-light);
}

.plan-features i {
  color: var(--success-color);
  font-size: var(--smaller-font-size);
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .search-form {
    flex-direction: column;
  }

  .job-stats {
    grid-template-columns: 1fr;
    gap: var(--mb-1);
  }

  .jobs-grid {
    grid-template-columns: 1fr;
  }

  .job-details {
    grid-template-columns: 1fr;
  }

  .job-actions {
    flex-direction: column;
  }

  .partner-content {
    grid-template-columns: 1fr;
  }

  .partner-benefits {
    grid-template-columns: 1fr;
  }

  .plans-grid {
    grid-template-columns: 1fr;
  }

  .plan-card.featured {
    transform: none;
  }

  .partner-actions {
    flex-direction: column;
  }

  .partner-actions .btn {
    width: 100%;
    justify-content: center;
  }
}

@media screen and (max-width: 480px) {
  .jobs-hero__title {
    font-size: 1.75rem;
  }

  .search-form {
    padding: 0.75rem;
  }

  .btn-search {
    padding: 0.75rem 1.5rem;
  }

  .job-card {
    padding: 1rem;
  }

  .job-footer {
    flex-direction: column;
    gap: var(--mb-0-5);
    align-items: stretch;
  }

  .job-actions {
    justify-content: center;
  }
}
