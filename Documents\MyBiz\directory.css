/* ===== DIRECTORY PAGE SPECIFIC STYLES ===== */

/* Main Content Fix */
.main {
  margin-top: 4rem; /* Account for fixed header height */
}

/* Page Header */
.page-header {
  padding: 6rem 0 2rem;
  background: var(--gradient-primary);
  color: white;
  text-align: center;
}

.page-header__content {
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: var(--h1-font-size);
  font-weight: var(--font-extra-bold);
  margin-bottom: var(--mb-1);
}

.page-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  line-height: 1.6;
}

/* Featured Jobs Section */
.featured-jobs {
  background-color: var(--container-color);
}

.jobs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--mb-2);
}

.job-card-featured {
  background: var(--card-color);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  border: 1px solid var(--border-color);
  position: relative;
}

.job-card-featured:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.job-card-featured::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-secondary);
}

.job-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--mb-1);
}

.job-company {
  display: flex;
  align-items: center;
  gap: var(--mb-0-5);
}

.company-logo {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius);
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  font-weight: var(--font-bold);
}

.company-info h4 {
  font-size: var(--normal-font-size);
  font-weight: var(--font-semi-bold);
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.company-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--secondary-color);
  font-size: var(--small-font-size);
}

.job-type-badge {
  background: var(--accent-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius);
  font-size: var(--smaller-font-size);
  font-weight: var(--font-medium);
}

.job-title {
  font-size: var(--h3-font-size);
  color: var(--text-color);
  margin-bottom: var(--mb-0-5);
}

.job-description {
  color: var(--text-color-light);
  font-size: var(--small-font-size);
  margin-bottom: var(--mb-1);
  line-height: 1.5;
}

.job-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--mb-0-5);
  margin-bottom: var(--mb-1);
}

.job-detail {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--small-font-size);
  color: var(--text-color-light);
}

.job-detail i {
  color: var(--primary-color);
  width: 16px;
}

.job-actions {
  display: flex;
  gap: var(--mb-0-5);
}

.btn-job {
  flex: 1;
  padding: 0.75rem;
  font-size: var(--small-font-size);
  justify-content: center;
}

/* Directory Filters */
.directory-filters {
  background-color: var(--body-color);
  border-bottom: 1px solid var(--border-color);
  padding: 2rem 0;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--mb-2);
}

.filters-header .section__title {
  margin-bottom: 0;
  text-align: left;
}

.filters-actions {
  display: flex;
  align-items: center;
  gap: var(--mb-1);
}

.view-toggle {
  display: flex;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.view-btn {
  padding: 0.5rem 0.75rem;
  background: var(--body-color);
  border: none;
  color: var(--text-color-light);
  cursor: pointer;
  transition: var(--transition);
}

.view-btn.active,
.view-btn:hover {
  background: var(--primary-color);
  color: white;
}

/* Filters Panel */
.filters-panel {
  background: var(--container-color);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  margin-bottom: var(--mb-2);
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.filters-panel.active {
  opacity: 1;
  max-height: 500px;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--mb-1-5);
  margin-bottom: var(--mb-2);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--mb-0-5);
}

.filter-label {
  font-weight: var(--font-medium);
  color: var(--text-color);
  font-size: var(--small-font-size);
}

.filter-input,
.filter-select {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--body-color);
  color: var(--text-color);
  font-size: var(--normal-font-size);
  transition: var(--transition);
}

.filter-input:focus,
.filter-select:focus {
  border-color: var(--primary-color);
  outline: none;
}

.search-input-container {
  position: relative;
}

.search-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-light);
}

.filter-actions {
  display: flex;
  gap: var(--mb-1);
  justify-content: flex-end;
}

/* Sort Options */
.sort-options {
  display: flex;
  align-items: center;
  gap: var(--mb-1);
  flex-wrap: wrap;
}

.sort-label {
  font-weight: var(--font-medium);
  color: var(--text-color);
  font-size: var(--small-font-size);
}

.sort-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.sort-btn {
  padding: 0.5rem 1rem;
  background: var(--body-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-color-light);
  font-size: var(--small-font-size);
  cursor: pointer;
  transition: var(--transition);
}

.sort-btn.active,
.sort-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Business Directory */
.business-directory {
  padding-top: 2rem;
}

.directory-stats {
  margin-bottom: var(--mb-2);
  text-align: center;
}

.results-count {
  color: var(--text-color-light);
  font-size: var(--small-font-size);
}

.results-count strong {
  color: var(--primary-color);
  font-weight: var(--font-bold);
}

.businesses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--mb-2);
  margin-bottom: var(--mb-3);
}

.businesses-grid.list-view {
  grid-template-columns: 1fr;
}

.business-card {
  background: var(--card-color);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  border: 1px solid var(--border-color);
  position: relative;
  cursor: pointer;
}

.business-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.business-card.verified::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--success-color);
}

.business-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--mb-1);
}

.business-main {
  display: flex;
  align-items: center;
  gap: var(--mb-0-75);
}

.business-logo {
  width: 60px;
  height: 60px;
  border-radius: var(--border-radius-lg);
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: var(--font-bold);
}

.business-info h3 {
  font-size: var(--h3-font-size);
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.business-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-bottom: 0.25rem;
}

.business-rating .stars {
  color: var(--secondary-color);
}

.business-rating .rating-text {
  color: var(--text-color-light);
  font-size: var(--small-font-size);
}

.business-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius);
  font-size: var(--smaller-font-size);
  font-weight: var(--font-medium);
}

.status-badge.verified {
  background: var(--success-color);
  color: white;
}

.status-badge.hiring {
  background: var(--accent-color);
  color: white;
}

.business-description {
  color: var(--text-color-light);
  font-size: var(--small-font-size);
  margin-bottom: var(--mb-1);
  line-height: 1.5;
}

.business-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: var(--mb-1);
}

.category-tag {
  background: var(--container-color);
  color: var(--text-color);
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius);
  font-size: var(--smaller-font-size);
  font-weight: var(--font-medium);
}

.business-info-icons {
  display: flex;
  gap: var(--mb-0-5);
  margin-bottom: var(--mb-1);
}

.business-actions {
  display: flex;
  gap: var(--mb-0-5);
}

.btn-business {
  flex: 1;
  padding: 0.75rem;
  font-size: var(--small-font-size);
  justify-content: center;
}

/* Load More */
.load-more-container {
  text-align: center;
  margin-top: var(--mb-3);
}

/* List View Styles */
.business-card.list-view {
  display: flex;
  align-items: center;
  gap: var(--mb-2);
  padding: 1rem 1.5rem;
}

.business-card.list-view .business-main {
  flex: 1;
}

.business-card.list-view .business-actions {
  flex-direction: column;
  min-width: 200px;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .filters-header {
    flex-direction: column;
    gap: var(--mb-1);
    align-items: stretch;
  }

  .filters-grid {
    grid-template-columns: 1fr;
  }

  .sort-options {
    flex-direction: column;
    align-items: flex-start;
  }

  .businesses-grid {
    grid-template-columns: 1fr;
  }

  .business-card.list-view {
    flex-direction: column;
    align-items: stretch;
  }

  .business-card.list-view .business-actions {
    min-width: auto;
  }

  .job-details {
    grid-template-columns: 1fr;
  }

  .job-actions {
    flex-direction: column;
  }
}
